#pragma once

#include <CoreMinimal.h>
#include <Blueprint/UserWidget.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageActionRequestChatItem.generated.h"

class ISageExtensionDelegator;
class UDruidsSageMessagingHandler;
class UTextBlock;
class UButton;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnActionAppliedUMG, const FString&, JsonString);

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageActionRequestChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageActionRequestChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeNameCpp() const override;
	virtual void FillInDruidsMessageCpp(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRoleCpp() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandlerCpp() const override;
	virtual void UpdateFromContentJsonCpp(const TSharedPtr<FJsonValue>& ContentJson) override;

	// Initialization methods
	void InitializeActionRequestChatItem(const TScriptInterface<ISageExtensionDelegator>& InExtensionDelegator);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(class UScrollBox* InScrollBox);

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Chat Item")
	FOnActionAppliedUMG OnActionApplied;

	static FName GetClassName() { return "UDruidsSageActionRequestChatItem"; }

protected:
	// BindWidget properties for Blueprint binding
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* RoleWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* MessageWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UButton* ApplyButton;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;
	TScriptInterface<ISageExtensionDelegator> ExtensionDelegator;
	TSharedPtr<FJsonValue> ContentJsonValue;

	UFUNCTION()
	void OnApplyButtonClicked();

	void SetupWidgets();
};
