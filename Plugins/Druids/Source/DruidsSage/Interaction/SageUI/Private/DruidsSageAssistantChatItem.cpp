#include "DruidsSageAssistantChatItem.h"

#include "DruidsSageActionRequestChatItem.h"
#include "DruidsSageSimpleChatItem.h"
#include "DruidsSageMessagingHandler.h"
#include "LogDruids.h"
#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "Components/ScrollBox.h"
#include "Components/VerticalBoxSlot.h"

UDruidsSageAssistantChatItem::UDruidsSageAssistantChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, RoleWidget(nullptr)
	, MessageContainer(nullptr)
	, PreviousContent<PERSON>son(nullptr)
{
}

void UDruidsSageAssistantChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	SetupWidgets();
}

void UDruidsSageAssistantChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	SetupWidgets();
	SetupMessagingHandler();
}

void UDruidsSageAssistantChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	SetupWidgets();
}

FName UDruidsSageAssistantChatItem::GetTypeNameCpp() const
{
	return GetClassName();
}

void UDruidsSageAssistantChatItem::FillInDruidsMessageCpp(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);

	TArray<TSharedPtr<FJsonValue>>* ContentArray;
	if (PreviousContentJson.IsValid())
	{
		if (PreviousContentJson->TryGetArray(ContentArray))
		{
			Message.SetContentArray(*ContentArray);
		}
		else
		{
			// If ContentJson isn't already an array, wrap it in one
			TArray<TSharedPtr<FJsonValue>> SingleItemArray = { PreviousContentJson };
			Message.SetContentArray(SingleItemArray);
		}
	}
	else
	{
		Message.SetChatContent(TEXT(""));
	}
}

EDruidsSageChatRole UDruidsSageAssistantChatItem::GetMessageRoleCpp() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageAssistantChatItem::GetMessagingHandlerCpp() const
{
	return MessagingHandler;
}

void UDruidsSageAssistantChatItem::UpdateFromContentJsonCpp(const TSharedPtr<FJsonValue>& ContentJson)
{
	PreviousContentJson = ContentJson;
	
	FString OutputString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(ContentJson, TEXT(""), Writer);
    
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("UDruidsSageAssistantChatItem::UpdateFromContentJsonCpp: %s"), *OutputString);
	
	//Update from a content array
	const TArray<TSharedPtr<FJsonValue>>* ContentArray = nullptr;
	if (!ContentJson->TryGetArray(ContentArray))
	{
		return;
	}

	if (!MessageContainer)
	{
		return;
	}

	int32 NumExistingWidgets = ChildChatItems.Num();
	
	for (int32 MessageIndex = 0; MessageIndex < ContentArray->Num(); MessageIndex++)
	{
		const TSharedPtr<FJsonValue>& JsonValue = (*ContentArray)[MessageIndex];
        
		if (MessageIndex < NumExistingWidgets)
		{
			// Update existing widget
			UIDruidsSageChatItem* DruidsSageChatItem = ChildChatItems[MessageIndex];
			if (!DruidsSageChatItem)
			{
				continue;
			}

			if (FString ContentType = GetContentType(JsonValue); ChatItemMatchesType(DruidsSageChatItem, ContentType))
			{
				UpdateChatItem(DruidsSageChatItem, JsonValue);
			}
			else
			{
				// Replace widget with new type
				MessageContainer->RemoveChild(DruidsSageChatItem);
				UIDruidsSageChatItem* NewChatItem = CreateNewChatItem(JsonValue);
				if (NewChatItem)
				{
					ChildChatItems[MessageIndex] = NewChatItem;
					UVerticalBoxSlot* VerticalSlot = MessageContainer->AddChildToVerticalBox(NewChatItem);
					if (VerticalSlot)
					{
						VerticalSlot->SetSize(FSlateChildSize(ESlateSizeRule::Fill));
					}
				}
			}
		}
		else
		{
			// Add new widget
			UIDruidsSageChatItem* NewChatItem = CreateNewChatItem(JsonValue);
			if (NewChatItem)
			{
				ChildChatItems.Add(NewChatItem);
				UVerticalBoxSlot* VerticalSlot = MessageContainer->AddChildToVerticalBox(NewChatItem);
				if (VerticalSlot)
				{
					VerticalSlot->SetSize(FSlateChildSize(ESlateSizeRule::Fill));
				}
			}
		}
	}
}

void UDruidsSageAssistantChatItem::InitializeAssistantChatItem()
{
	SetupWidgets();
	SetupMessagingHandler();
}

void UDruidsSageAssistantChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	if (MessagingHandler.IsValid())
	{
		// Note: ScrollBoxReference expects a TSharedPtr<SScrollBox>, but we have UScrollBox*`n`t`t// This will need to be addressed when MessagingHandler is updated for UMG
	}
}

void UDruidsSageAssistantChatItem::SetRawText(const FString& ContentText)
{
	const TSharedPtr<FJsonValueArray> JsonValueArray = CreateTextContentJson(ContentText);
	UpdateFromContentJsonCpp(JsonValueArray);
}

void UDruidsSageAssistantChatItem::SetupMessagingHandler()
{
	MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
	MessagingHandler->SetFlags(RF_Standalone);

	MessagingHandler->OnMessageRequestSent.BindLambda([this](FString Content)
	{
		SetRawText(Content);
	});

	MessagingHandler->OnMessageRequestFailed.BindLambda([this](FString Content)
	{
		SetRawText(Content);
	});

	MessagingHandler->OnMessageResponseUpdated.BindLambda([this](const FDruidsSageChatResponse& Response)
	{
		if (Response.bSuccess)
		{
			if (!Response.Choices.IsEmpty())
			{
				UpdateFromDruidsSageMessage(&Response.Choices.Top().Message);
			}
			else
			{
				FString Content = CreateProcessingText();
				SetRawText(Content);
			}
		}
		else
		{
			UE_LOG(LogDruidsSage, Error, TEXT("UDruidsSageAssistantChatItem::OnMessageResponseUpdated: Response is not valid."));
		}
	});
}

void UDruidsSageAssistantChatItem::SetupWidgets()
{
	if (RoleWidget)
	{
		RoleWidget->SetText(FText::FromString(TEXT("Sage:")));
	}
}

void UDruidsSageAssistantChatItem::UpdateFromDruidsSageMessage(const FDruidsSageChatMessage* Message)
{
	const TArray<TSharedPtr<FJsonValue>>& ContentArray = Message->GetContentArray();
	const TSharedPtr<FJsonValue> JsonValue = MakeShared<FJsonValueArray>(ContentArray);
	UpdateFromContentJsonCpp(JsonValue);
}

UIDruidsSageChatItem* UDruidsSageAssistantChatItem::CreateNewChatItem(const TSharedPtr<FJsonValue>& ContentJson)
{
	FString ContentType = GetContentType(ContentJson);

	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (ContentJson.Get()->TryGetObject(JsonObject))
	{
		if (ContentType == TEXT("text"))
		{
			FString Text;
			JsonObject->Get()->TryGetStringField(TEXT("text"), Text);

			UDruidsSageSimpleChatItem* SimpleChatItem = CreateWidget<UDruidsSageSimpleChatItem>(this);
			if (SimpleChatItem && MessagingHandler.IsValid())
			{
				SimpleChatItem->InitializeSimpleChatItem(EDruidsSageChatRole::Assistant, Text, TArray<TSharedPtr<FDruidsSageExtensionDefinition>>());
				// Note: ScrollBoxReference type mismatch - will be fixed when MessagingHandler is updated for UMG
				// SimpleChatItem->SetScrollBoxReference(MessagingHandler->ScrollBoxReference.Get());
			}
			return SimpleChatItem;
		}
		if (ContentType == TEXT("action_request"))
		{
			if (const TSharedPtr<FJsonObject>* ActionRequest;
				JsonObject->Get()->TryGetObjectField(TEXT("action_request"), ActionRequest))
			{
				UDruidsSageActionRequestChatItem* ActionRequestChatItem = CreateWidget<UDruidsSageActionRequestChatItem>(this);
				if (ActionRequestChatItem && MessagingHandler.IsValid())
				{
					// Note: ScrollBoxReference type mismatch - will be fixed when MessagingHandler is updated for UMG
				// ActionRequestChatItem->SetScrollBoxReference(MessagingHandler->ScrollBoxReference.Get());
					ActionRequestChatItem->OnActionApplied.AddDynamic(this, &UDruidsSageAssistantChatItem::HandleActionRequestApplied);

					TSharedPtr<FJsonValue> ActionRequestValue = MakeShared<FJsonValueObject>(*ActionRequest);
					ActionRequestChatItem->UpdateFromContentJsonCpp(ActionRequestValue);
				}
				return ActionRequestChatItem;
			}
		}
	}

	// Default fallback
	UDruidsSageSimpleChatItem* SimpleChatItem = CreateWidget<UDruidsSageSimpleChatItem>(this);
	if (SimpleChatItem && MessagingHandler.IsValid())
	{
		SimpleChatItem->InitializeSimpleChatItem(EDruidsSageChatRole::Assistant, TEXT(""), TArray<TSharedPtr<FDruidsSageExtensionDefinition>>());
		// Note: ScrollBoxReference type mismatch - will be fixed when MessagingHandler is updated for UMG
	// SimpleChatItem->SetScrollBoxReference(MessagingHandler->ScrollBoxReference.Get());
	}
	return SimpleChatItem;
}

void UDruidsSageAssistantChatItem::UpdateChatItem(UIDruidsSageChatItem* ChatItem, const TSharedPtr<FJsonValue>& ContentJson)
{
	if (ChatItem)
	{
		ChatItem->UpdateFromContentJsonCpp(ContentJson);
	}
}

void UDruidsSageAssistantChatItem::HandleActionRequestApplied(const FString& JsonString)
{
	if (OnActionApplied.IsBound())
	{
		OnActionApplied.Broadcast(JsonString);
	}
}

// Static helper methods
FString UDruidsSageAssistantChatItem::GetContentType(const TSharedPtr<FJsonValue>& ContentJson)
{
	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (ContentJson.Get()->TryGetObject(JsonObject))
	{
		FString Type;
		JsonObject->Get()->TryGetStringField(TEXT("type"), Type);
		return Type;
	}
	return TEXT("Unknown");
}

TSharedPtr<FJsonValueArray> UDruidsSageAssistantChatItem::CreateTextContentJson(const FString& Content)
{
	TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
	JsonObject->SetStringField(TEXT("type"), TEXT("text"));
	JsonObject->SetStringField(TEXT("text"), Content);

	const TArray<TSharedPtr<FJsonValue>> ContentArray {
		MakeShared<FJsonValueObject>(JsonObject)
	};
	return MakeShared<FJsonValueArray>(ContentArray);
}

FString UDruidsSageAssistantChatItem::CreateProcessingText()
{
	FString Content;

	switch (constexpr int SelectedAnimation = 1)
	{
	case 1:
		{
			static int MessageIndex = 0;
			static const TArray<FString> Messages = {
				TEXT("Processing your request"),
				TEXT("Checking your context"),
				TEXT("Understanding"),
				TEXT("Thinking a bit"),
				TEXT("Looking at what I can do"),
				TEXT("Generating a response"),
				TEXT("Figuring out what you want"),
				TEXT("Thinking some more"),
				TEXT("Considering"),
				TEXT("Consulting some references"),
				TEXT("Analyzing"),
			};

			constexpr int RepeatMessageTimes = 2;

			Content = Messages[MessageIndex / RepeatMessageTimes];

			// Rotate to next message
			MessageIndex = (MessageIndex + 1) % (Messages.Num() * RepeatMessageTimes);
		}
		break;
	default:
		Content = TEXT("Processing...");
		break;
	}

	return Content;
}

bool UDruidsSageAssistantChatItem::ChatItemMatchesType(UIDruidsSageChatItem* Widget, const FString& Type)
{
	if (!Widget)
	{
		return false;
	}

	static const TMap<FString, FName> TypeToClass = {
		{ TEXT("text"), UDruidsSageSimpleChatItem::StaticClass()->GetFName() },
		{ TEXT("action_request"), UDruidsSageActionRequestChatItem::StaticClass()->GetFName() }
	};

	if (const FName* ClassName = TypeToClass.Find(Type))
	{
		return Widget->GetTypeNameCpp() == *ClassName;
	}
	return false;
}
