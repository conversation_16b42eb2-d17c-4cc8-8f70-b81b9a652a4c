#include "DruidsSageSimpleChatItem.h"

#include "CustomMarkdownDecorator.h"
#include "DruidsSageMessagingHandler.h"
#include "MarkdownRichTextBlock.h"
#include "Components/TextBlock.h"
#include "Components/ScrollBox.h"

UDruidsSageSimpleChatItem::UDruidsSageSimpleChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, RoleWidget(nullptr)
	, MessageWidget(nullptr)
	, MessageRole(EDruidsSageChatRole::User)
	, ChatText(TEXT(""))
{
}

void UDruidsSageSimpleChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	UpdateWidgetStyling();
}

FName UDruidsSageSimpleChatItem::GetTypeNameCpp() const
{
	return GetClassName();
}

void UDruidsSageSimpleChatItem::FillInDruidsMessageCpp(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(MessageRole);
	Message.SetChatContent(ChatText);
	Message.SetActiveExtensionDefinitions(ActiveExtensionDefinitions);
}

EDruidsSageChatRole UDruidsSageSimpleChatItem::GetMessageRoleCpp() const
{
	return MessageRole;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageSimpleChatItem::GetMessagingHandlerCpp() const
{
	return MessagingHandler;
}

void UDruidsSageSimpleChatItem::UpdateFromContentJsonCpp(const TSharedPtr<FJsonValue>& ContentJson)
{
	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	FString Type;
	(*JsonObject)->TryGetStringField(TEXT("type"), Type);
	if (Type == TEXT("text"))
	{
		FString Text;
		(*JsonObject)->TryGetStringField(TEXT("text"), Text);
		
		ChatText = Text;
		if (MessageWidget)
		{
			MessageWidget->SetNewText(FText::FromString(Text));
		}
	}
}

void UDruidsSageSimpleChatItem::InitializeSimpleChatItem(EDruidsSageChatRole InMessageRole, const FString& InChatText, 
	const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& InActiveExtensionDefinitions)
{
	MessageRole = InMessageRole;
	ChatText = InChatText;
	ActiveExtensionDefinitions = InActiveExtensionDefinitions;

	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	SetupMessagingHandler(InScrollBox);
}

void UDruidsSageSimpleChatItem::SetupMessagingHandler(UScrollBox* ScrollBox)
{
	if (MessageRole == EDruidsSageChatRole::Assistant)
	{
		MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
		MessagingHandler->SetFlags(RF_Standalone);
		// Note: ScrollBoxReference expects a TSharedPtr<SScrollBox>, but we have UScrollBox*
		// This will need to be addressed when MessagingHandler is updated for UMG

		MessagingHandler->OnMessageContentUpdated.BindLambda([this](FString Content)
		{
			if (!MessageWidget)
			{
				return;
			}

			ChatText = Content;
			MessageWidget->SetNewText(FText::FromString(Content));
		});
	}
}

void UDruidsSageSimpleChatItem::UpdateWidgetStyling()
{
	if (!RoleWidget || !MessageWidget)
	{
		return;
	}

	FText RoleText = FText::FromString(TEXT("User:"));
	
	if (MessageRole == EDruidsSageChatRole::Assistant)
	{
		RoleText = FText::FromString(TEXT("Response:"));
	}
	else if (MessageRole == EDruidsSageChatRole::System)
	{
		RoleText = FText::FromString(TEXT("System:"));
	}

	RoleWidget->SetText(RoleText);
	MessageWidget->SetNewText(FText::FromString(ChatText));
}
