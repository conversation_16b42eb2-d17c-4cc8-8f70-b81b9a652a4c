#include "MarkdownRichTextBlock.h"
#include "Internationalization/Regex.h"

UMarkdownRichTextBlock::UMarkdownRichTextBlock(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	// Set default properties
	SetAutoWrapText(bAutoWrapText);
	SetWrapTextAt(WrapTextAt);
	SetMargin(TextMargin);
	SetLineHeightPercentage(LineHeightPercentage);
	SetJustification(TextJustification);
}

void UMarkdownRichTextBlock::SynchronizeProperties()
{
	Super::SynchronizeProperties();

	// Apply properties to the underlying widget
	SetAutoWrapText(bAutoWrapText);
	SetWrapTextAt(WrapTextAt);
	SetMargin(TextMargin);
	SetLineHeightPercentage(LineHeightPercentage);
	SetJustification(TextJustification);

	if (!HighlightText.IsEmpty())
	{
		SetHighlightText(HighlightText);
	}
}

void UMarkdownRichTextBlock::SetNewText(const FText& InNewText)
{
    FText ProcessedText = ProcessMarkdownAttribute(InNewText);
    SetText(ProcessedText);
}

FText SMarkdownRichTextBlock::ProcessMarkdownAttribute(const FText& RawMarkdown)
{
    // Convert to FString and split into lines.
    FString InputString = RawMarkdown.ToString();
    TArray<FString> Lines;
    InputString.ParseIntoArrayLines(Lines);

    FString ProcessedString;

    bool bInCodeBlock = false; // Tracks whether we are inside a code block
    
    // Process each line as a block-level element.
    for (const FString& Line : Lines)
    {
        FString OutLine;

        // --- Check for Code Block Start/End ---
        if (Line.StartsWith(TEXT("```")))
        {
            // Toggle code block mode ON or OFF
            if (bInCodeBlock)
            {
                // End of the code block, process and add formatted content.
                ProcessedString += LINE_TERMINATOR;
            }
            else
            {
                // Capture the code block type
                FString CodeBlockType = Line.Mid(3);
                ProcessedString += FString::Printf(TEXT("<CodeBlockType>%s</>"), *CodeBlockType) +
                    LINE_TERMINATOR + LINE_TERMINATOR;
            }

            bInCodeBlock = !bInCodeBlock;
            continue; // Skip further processing of this line
        }

        // --- Handle lines inside a code block ---
        if (bInCodeBlock)
        {
            ProcessedString += FString::Printf(TEXT("<CodeBlock>%s</>"), *Line) + LINE_TERMINATOR;

            continue; // Skip further processing of this line
        }

        // --- Check for a Heading (lines starting with 1-6 '#' characters) ---
        {
            // Pattern: one or more '#' at the beginning followed by whitespace and then the heading text.
            FRegexPattern HeaderPattern(TEXT("^(#{1,6})\\s*(.*)$"));
            FRegexMatcher HeaderMatcher(HeaderPattern, Line);
            if (HeaderMatcher.FindNext())
            {
                FString Hashes      = HeaderMatcher.GetCaptureGroup(1);
                FString HeadingText = HeaderMatcher.GetCaptureGroup(2);
                int32   Level       = Hashes.Len();

                // Build the flat output: each inline piece inside the heading is handled separately.
                OutLine += FString::Printf(TEXT("<HeadingSpacing%d></>"), Level) + LINE_TERMINATOR;
                OutLine += FString::Printf(TEXT("<HeadingSpacing%d></>"), Level) + LINE_TERMINATOR;
                OutLine += ProcessInlineFormatting(HeadingText, FString::Printf(TEXT("Heading%d"), Level));
                OutLine += LINE_TERMINATOR;
                
                ProcessedString += OutLine;

                continue; // Processed this line, move to the next.
            }
        }

        // --- Check for an Unordered List Item (starting with "-" or "*") ---
        {
            FRegexPattern UnorderedListPattern(TEXT("^\\s*[-*]\\s+(.*)$"));
            FRegexMatcher UnorderedListMatcher(UnorderedListPattern, Line);
            if (UnorderedListMatcher.FindNext())
            {
                FString ListItemContent = UnorderedListMatcher.GetCaptureGroup(1);
                OutLine += FString("<NormalSpacing></>") + LINE_TERMINATOR;
                OutLine += TEXT("<UnorderedListItem></>") +
                    ProcessInlineFormatting(ListItemContent, TEXT("Paragraph"));
                OutLine += LINE_TERMINATOR;

                ProcessedString += OutLine;
                
                continue;
            }
        }

        // --- Check for an Ordered List Item (starting with "1. ", "2. ", etc.) ---
        {
            FRegexPattern OrderedListPattern(TEXT("^\\s*(\\d+\\. )(.*)$"));
            FRegexMatcher OrderedListMatcher(OrderedListPattern, Line);
            if (OrderedListMatcher.FindNext())
            {

                FString ListItemNumber = OrderedListMatcher.GetCaptureGroup(1);
                FString ListItemContent = OrderedListMatcher.GetCaptureGroup(2);
                OutLine += FString("<NormalSpacing></>") + LINE_TERMINATOR;
                OutLine += FString("<NormalSpacing></>") + LINE_TERMINATOR;
                OutLine += FString::Printf(TEXT("<OrderedListItem>%s</>"), *ListItemNumber) +
                    ProcessInlineFormatting(ListItemContent, TEXT("Paragraph"));
                OutLine += LINE_TERMINATOR;

                ProcessedString += OutLine; 
                continue;
            }
        }

        // --- Otherwise treat the line as a Paragraph ---
        OutLine += FString("<NormalSpacing></>") + LINE_TERMINATOR;
        OutLine += FString("<NormalSpacing></>") + LINE_TERMINATOR;
        OutLine += ProcessInlineFormatting(Line, TEXT("Paragraph"));
        ProcessedString += OutLine + LINE_TERMINATOR;
    }

    return FText::FromString(ProcessedString);
}

// New signature: bParentBold defaults to false.
FString SMarkdownRichTextBlock::ProcessInlineFormatting(const FString& InputText, const FString& BlockTag,
                                                        bool bUseBlockTag /*= true*/,
                                                        bool bParentBold /*= false*/, bool bParentItalic /*= false*/)
{
    FString Result;
    int32 CurrentIndex = 0;
    const int32 InputLength = InputText.Len();

    while (CurrentIndex < InputLength)
    {
        const FString Bold1Marker("**");
    	const FString Bold2Marker("__");
    	const FString Italic1Marker("*");
    	const FString Italic2Marker("_");
    	const FString CodeMarker("`");
    	const FString StrikethroughMarker("~~");
    	
    	// Find the next occurrence of any inline markdown marker.
        int32 Bold1Index      = InputText.Find(Bold1Marker, ESearchCase::IgnoreCase, ESearchDir::FromStart, CurrentIndex);
        int32 Bold2Index      = InputText.Find(Bold2Marker, ESearchCase::IgnoreCase, ESearchDir::FromStart, CurrentIndex);
        int32 Italic1Index    = InputText.Find(Italic1Marker,  ESearchCase::IgnoreCase, ESearchDir::FromStart, CurrentIndex);
        int32 Italic2Index    = InputText.Find(Italic2Marker,  ESearchCase::IgnoreCase, ESearchDir::FromStart, CurrentIndex);
        int32 CodeIndex      = InputText.Find(CodeMarker,  ESearchCase::IgnoreCase, ESearchDir::FromStart, CurrentIndex);
        int32 StrikeIndex    = InputText.Find(StrikethroughMarker, ESearchCase::IgnoreCase, ESearchDir::FromStart, CurrentIndex);

        // Determine the earliest marker (if any).
        int32 EarliestIndex = INDEX_NONE;
        FString Marker;
        auto ConsiderMarker = [&EarliestIndex, &Marker, CurrentIndex](int32 FoundIndex, const FString& ThisMarker)
        {
            if (FoundIndex != INDEX_NONE && (EarliestIndex == INDEX_NONE || FoundIndex < EarliestIndex))
            {
                EarliestIndex = FoundIndex;
                Marker = ThisMarker;
            }
        };

        ConsiderMarker(Bold1Index, Bold1Marker);
        ConsiderMarker(Bold2Index, Bold2Marker);
        ConsiderMarker(Italic1Index, Italic1Marker);
        ConsiderMarker(Italic2Index, Italic2Marker);
        ConsiderMarker(CodeIndex, CodeMarker);
        ConsiderMarker(StrikeIndex, StrikethroughMarker);

        if (EarliestIndex == INDEX_NONE)
        {
            // No more markers found; output the remainder as plain text.
            FString PlainText = InputText.Mid(CurrentIndex);
            if (!PlainText.IsEmpty())
            {
                if (bUseBlockTag)
                {
                    Result += FString::Printf(TEXT("<%s>%s</>"), *BlockTag, *PlainText);
                }
                else
                {
                    Result += FString::Printf(TEXT("<%s>%s</>"), TEXT("Paragraph"), *PlainText);
                }
            }
            break;
        }

        // Output any plain text before the marker.
        if (EarliestIndex > CurrentIndex)
        {
            FString PlainText = InputText.Mid(CurrentIndex, EarliestIndex - CurrentIndex);
            
            if (bUseBlockTag)
            {
                Result += FString::Printf(TEXT("<%s>%s</>"), *BlockTag, *PlainText);
            }
            else
            {
                Result += FString::Printf(TEXT("<%s>%s</>"), TEXT("Paragraph"), *PlainText);
            }
        }

        // Find the closing marker.
        int32 MarkerLength = Marker.Len();
        int32 ClosingIndex = InputText.Find(Marker, ESearchCase::IgnoreCase, ESearchDir::FromStart, EarliestIndex + MarkerLength);
        if (ClosingIndex == INDEX_NONE)
        {
            // If no closing marker is found, treat the remainder as plain text.
            FString Remaining = InputText.Mid(EarliestIndex);

            if (bUseBlockTag)
            {
                Result += FString::Printf(TEXT("<%s>%s</>"), *BlockTag, *Remaining);
            }
            else
            {
                Result += *Remaining;
            }
            
            break;
        }

        // Extract the content between the markers.
        int32 ContentStart = EarliestIndex + MarkerLength;
        int32 ContentLength = ClosingIndex - ContentStart;
        FString InlineContent = InputText.Mid(ContentStart, ContentLength);

        // Process based on which marker was found.
        if (Marker == Bold1Marker || Marker == Bold2Marker)
        {
            // If we're inside an Italic context, then italic becomes Bold-Italic.
            FString InlineTag = bParentItalic ? TEXT("Bold-Italic") : TEXT("Bold");

            // When encountering Bold, extract the inner text and process it recursively.
            // Here, we pass "Bold" as the BlockTag and true for bParentBold.
            FString ProcessedInner = ProcessInlineFormatting(InlineContent, InlineTag, bUseBlockTag, true);
            Result += *ProcessedInner;
        }
        else if (Marker == Italic1Marker || Marker == Italic2Marker)
        {
            // If we're inside a Bold context, then italic becomes Bold-Italic.
            FString InlineTag = bParentBold ? TEXT("Bold-Italic") : TEXT("Italic");

            // When encountering Bold, extract the inner text and process it recursively.
            // Here, we pass "Bold" as the BlockTag and true for bParentBold.
            FString ProcessedInner = ProcessInlineFormatting(InlineContent, InlineTag, bUseBlockTag, bParentBold, true);
            Result += *ProcessedInner;
        }
        else if (Marker == CodeMarker)
        {
            Result += FString::Printf(TEXT("<Code>%s</>"), *InlineContent);
        }
        else if (Marker == StrikethroughMarker)
        {
            Result += FString::Printf(TEXT("<Strikethrough>%s</>"), *InlineContent);
        }
        else
        {
            // Fallback: output the content using the current block tag.
            if (bUseBlockTag)
            {
                Result += FString::Printf(TEXT("<%s>%s</>"), *BlockTag, *InlineContent);
            }
            else
            {
                Result += *InlineContent;
            }
        }

        // Move the index past the closing marker.
        CurrentIndex = ClosingIndex + MarkerLength;
    }

    return Result;
}
