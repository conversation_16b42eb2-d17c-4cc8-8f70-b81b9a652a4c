#pragma once

#include <CoreMinimal.h>
#include <Blueprint/UserWidget.h>

#include "DruidsSageChatTypes.h"

#include "IDruidsSageChatItem.generated.h"

class UDruidsSageMessagingHandler;
struct FDruidsSageExtensionDefinition;

UCLASS(Abstract, BlueprintType, Blueprintable)
class SAGECORE_API UIDruidsSageChatItem : public UUserWidget
{
    GENERATED_BODY()

public:
    // C++ virtual methods for implementation
    virtual FName GetTypeNameCpp() const PURE_VIRTUAL(UIDruidsSageChatItem::GetTypeNameCpp, return NAME_None;);
    virtual void FillInDruidsMessageCpp(FDruidsSageChatMessage& Message) const PURE_VIRTUAL(UIDruidsSageChatItem::FillInDruidsMessageCpp, );
    virtual EDruidsSageChatRole GetMessageRoleCpp() const PURE_VIRTUAL(UIDruidsSageChatItem::GetMessageRoleCpp, return EDruidsSageChatRole::User;);
    virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandlerCpp() const PURE_VIRTUAL(UIDruidsSageChatItem::GetMessagingHandlerCpp, return nullptr;);
    virtual void UpdateFromContentJsonCpp(const TSharedPtr<FJsonValue>& ContentJson) PURE_VIRTUAL(UIDruidsSageChatItem::UpdateFromContentJsonCpp, );

    // Blueprint-compatible versions using strings instead of TSharedPtr
    UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Chat Item")
    FName GetTypeName() const;

    UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Chat Item")
    void FillInDruidsMessage(UPARAM(ref) FDruidsSageChatMessage& Message) const;

    UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Chat Item")
    EDruidsSageChatRole GetMessageRole() const;

    UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Chat Item")
    UDruidsSageMessagingHandler* GetMessagingHandler() const;

    UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Chat Item")
    void UpdateFromContentJsonString(const FString& ContentJsonString);
};
